#!/bin/bash

# Anaconda环境设置脚本
# 使用方法: ./scripts/setup_conda.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 环境名称
ENV_NAME="money-plan"
PYTHON_VERSION="3.11"

log_info "开始设置Anaconda环境..."

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    log_error "Conda未找到，请确保已安装Anaconda或Miniconda"
    exit 1
fi

log_success "Conda已安装: $(conda --version)"

# 检查环境是否已存在
if conda env list | grep -q "^${ENV_NAME}"; then
    log_warning "环境 ${ENV_NAME} 已存在"
    read -p "是否删除并重新创建? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "删除现有环境..."
        conda env remove -n ${ENV_NAME} -y
    else
        log_info "使用现有环境"
    fi
fi

# 创建conda环境
if ! conda env list | grep -q "^${ENV_NAME}"; then
    log_info "创建conda环境: ${ENV_NAME} (Python ${PYTHON_VERSION})"
    conda create -n ${ENV_NAME} python=${PYTHON_VERSION} -y
    log_success "环境创建完成"
fi

# 激活环境并安装依赖
log_info "激活环境并安装依赖..."
eval "$(conda shell.bash hook)"
conda activate ${ENV_NAME}

# 升级pip
pip install --upgrade pip

# 安装基础依赖
log_info "安装Python依赖包..."
pip install -r requirements.txt

# 安装conda特定的包（如果需要）
log_info "安装conda包..."
conda install -c conda-forge ffmpeg -y

log_success "所有依赖安装完成"

# 创建conda启动脚本
log_info "创建conda启动脚本..."
cat > start_conda.sh <<EOF
#!/bin/bash
# Conda环境启动脚本

# 初始化conda
eval "\$(conda shell.bash hook)"

# 激活环境
conda activate ${ENV_NAME}

# 检查环境
if [ "\$CONDA_DEFAULT_ENV" != "${ENV_NAME}" ]; then
    echo "错误: 无法激活conda环境 ${ENV_NAME}"
    exit 1
fi

echo "已激活conda环境: \$CONDA_DEFAULT_ENV"
echo "Python版本: \$(python --version)"

# 启动应用
python main.py
EOF

chmod +x start_conda.sh

# 创建conda停止脚本
cat > stop_conda.sh <<EOF
#!/bin/bash
# 停止应用
pkill -f "python main.py"
echo "应用已停止"
EOF

chmod +x stop_conda.sh

# 创建环境测试脚本
cat > test_conda_env.sh <<EOF
#!/bin/bash
# 测试conda环境

eval "\$(conda shell.bash hook)"
conda activate ${ENV_NAME}

echo "测试conda环境..."
echo "Python版本: \$(python --version)"
echo "Conda环境: \$CONDA_DEFAULT_ENV"

# 测试关键依赖
python -c "
import sys
print(f'Python路径: {sys.executable}')

# 测试关键模块
modules = [
    'fastapi', 'uvicorn', 'pydantic', 'python-dotenv',
    'openai', 'anthropic', 'pandas', 'numpy', 'requests',
    'beautifulsoup4', 'sqlalchemy', 'apscheduler', 'moviepy',
    'pydub', 'aiohttp', 'loguru'
]

failed = []
for module in modules:
    try:
        __import__(module)
        print(f'✓ {module}')
    except ImportError as e:
        print(f'✗ {module}: {e}')
        failed.append(module)

if failed:
    print(f'\\n失败的模块: {failed}')
    sys.exit(1)
else:
    print('\\n所有模块测试通过!')
"

# 测试FFmpeg
if command -v ffmpeg &> /dev/null; then
    echo "✓ FFmpeg: \$(ffmpeg -version | head -n1)"
else
    echo "✗ FFmpeg未找到"
fi

echo "环境测试完成"
EOF

chmod +x test_conda_env.sh

log_success "Conda环境设置完成!"
echo
log_info "创建的文件:"
echo "  - start_conda.sh    # 使用conda环境启动应用"
echo "  - stop_conda.sh     # 停止应用"
echo "  - test_conda_env.sh # 测试环境"
echo
log_info "下一步操作:"
echo "1. 配置API密钥: 编辑 .env 文件"
echo "2. 测试环境: ./test_conda_env.sh"
echo "3. 启动应用: ./start_conda.sh"
echo
log_warning "注意: 每次运行前都会自动激活 ${ENV_NAME} 环境"
