version: '3.8'

services:
  app:
    build: .
    container_name: money_plan_app
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=sqlite:///./data/app.db
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - money_plan_network

  redis:
    image: redis:7-alpine
    container_name: money_plan_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - money_plan_network

  nginx:
    image: nginx:alpine
    container_name: money_plan_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - money_plan_network

volumes:
  redis_data:

networks:
  money_plan_network:
    driver: bridge
