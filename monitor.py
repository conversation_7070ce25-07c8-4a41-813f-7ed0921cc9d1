#!/usr/bin/env python3
"""
系统监控脚本
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.utils.database import get_db_manager
from src.utils.ai_client import get_ai_client
from src.config.settings import get_settings

def print_header(title):
    """打印标题"""
    print(f"\n{'='*50}")
    print(f"  {title}")
    print(f"{'='*50}")

def print_section(title):
    """打印章节"""
    print(f"\n📋 {title}")
    print("-" * 30)

async def main():
    """主监控函数"""
    print_header("自动化内容创作系统 - 状态监控")
    print(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 系统配置状态
    print_section("系统配置")
    settings = get_settings()
    print(f"应用名称: {settings.app_name}")
    print(f"版本: {settings.version}")
    print(f"环境: {settings.environment}")
    print(f"调试模式: {settings.debug}")
    print(f"执行间隔: {settings.schedule_interval_hours}小时")
    print(f"每次处理文章数: {settings.max_articles_per_run}")
    
    # 2. AI服务状态
    print_section("AI服务状态")
    ai_client = get_ai_client()
    providers = ai_client.get_available_providers()
    
    if providers:
        print("✅ 可用的AI服务:")
        for provider in providers:
            print(f"   • {provider.value}")
    else:
        print("❌ 未配置AI服务")
    
    # 3. 数据库统计
    print_section("数据库统计")
    db = get_db_manager()
    stats = db.get_statistics()
    
    print(f"📰 文章统计:")
    print(f"   总文章数: {stats['total_articles']}")
    print(f"   已处理: {stats['processed_articles']}")
    print(f"   未处理: {stats['total_articles'] - stats['processed_articles']}")
    
    print(f"\n📖 故事统计:")
    print(f"   提取故事数: {stats['total_stories']}")
    print(f"   高质量故事: {stats['suitable_stories']}")
    
    print(f"\n🎬 视频统计:")
    print(f"   生成视频数: {stats['total_videos']}")
    print(f"   已发布: {stats['published_videos']}")
    
    print(f"\n📤 发布统计:")
    print(f"   成功发布: {stats['successful_publishes']}")
    
    # 4. 最新文章
    print_section("最新获取的文章")
    articles = db.get_unprocessed_articles(5)
    
    if articles:
        for i, article in enumerate(articles, 1):
            print(f"{i}. {article.title}")
            print(f"   来源: {article.source} | 热度: {article.hot_score}")
            if article.content:
                content_preview = article.content[:100] + "..." if len(article.content) > 100 else article.content
                print(f"   内容: {content_preview}")
            print()
    else:
        print("暂无文章数据")
    
    # 5. 系统健康检查
    print_section("系统健康检查")
    
    # 检查日志文件
    log_files = ['logs/app.log', 'logs/error.log', 'logs/tasks.log']
    for log_file in log_files:
        if Path(log_file).exists():
            size = Path(log_file).stat().st_size
            print(f"✅ {log_file}: {size} bytes")
        else:
            print(f"❌ {log_file}: 不存在")
    
    # 检查数据目录
    data_dirs = ['data', 'data/videos', 'logs']
    for data_dir in data_dirs:
        if Path(data_dir).exists():
            print(f"✅ {data_dir}/: 存在")
        else:
            print(f"❌ {data_dir}/: 不存在")
    
    # 6. 运行建议
    print_section("运行建议")
    
    if stats['total_articles'] == 0:
        print("💡 建议: 系统刚启动，等待定时任务执行或手动触发")
    elif stats['total_stories'] == 0:
        print("💡 建议: 已获取文章但未提取到故事，可能需要:")
        print("   • 检查AI服务配置")
        print("   • 调整故事筛选条件")
    elif stats['total_videos'] == 0:
        print("💡 建议: 已提取故事但未生成视频，可能需要:")
        print("   • 安装MoviePy: pip install moviepy")
        print("   • 检查FFmpeg安装")
    elif stats['published_videos'] == 0:
        print("💡 建议: 已生成视频但未发布，检查:")
        print("   • 头条号API配置")
        print("   • 网络连接")
    else:
        print("🎉 系统运行正常！")
    
    print_section("快速操作")
    print("手动执行任务:")
    print("  python -c \"import asyncio; from src.scheduler.tasks import TaskScheduler; asyncio.run(TaskScheduler().run_pipeline_now())\"")
    print("\n查看实时日志:")
    print("  tail -f logs/app.log")
    print("\n停止系统:")
    print("  pkill -f 'python main.py'")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n监控被用户中断")
    except Exception as e:
        print(f"\n监控过程中发生错误: {e}")
        sys.exit(1)
