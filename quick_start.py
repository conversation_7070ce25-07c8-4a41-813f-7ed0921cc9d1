#!/usr/bin/env python3
"""
快速启动脚本 - 用于测试系统基本功能
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.utils.logger import get_logger
from src.config.settings import get_settings
from src.utils.database import get_db_manager
from src.utils.ai_client import get_ai_client

logger = get_logger(__name__)

async def test_system():
    """测试系统各个组件"""
    print("🚀 开始测试自动化内容创作系统...")
    
    # 1. 测试配置
    print("\n1. 测试配置系统...")
    settings = get_settings()
    print(f"   ✓ 应用名称: {settings.app_name}")
    print(f"   ✓ 版本: {settings.version}")
    print(f"   ✓ 环境: {settings.environment}")
    print(f"   ✓ 调试模式: {settings.debug}")
    
    # 2. 测试数据库
    print("\n2. 测试数据库...")
    db = get_db_manager()
    stats = db.get_statistics()
    print(f"   ✓ 数据库连接正常")
    print(f"   ✓ 统计信息: {stats}")
    
    # 3. 测试AI客户端
    print("\n3. 测试AI客户端...")
    ai_client = get_ai_client()
    available_providers = ai_client.get_available_providers()
    print(f"   ✓ 可用的AI服务: {[p.value for p in available_providers]}")
    
    if not available_providers:
        print("   ⚠️  警告: 未配置AI服务API密钥")
        print("   💡 请在.env文件中配置OPENAI_API_KEY或ANTHROPIC_API_KEY")
    
    # 4. 测试核心模块导入
    print("\n4. 测试核心模块...")
    try:
        from src.core.news_crawler import NewsCrawler
        from src.core.content_extractor import StoryExtractor
        from src.core.story_rewriter import StoryRewriter
        from src.core.publisher import ToutiaoPublisher
        from src.scheduler.tasks import TaskScheduler
        
        print("   ✓ 新闻爬虫模块")
        print("   ✓ 内容提取模块")
        print("   ✓ 故事改写模块")
        print("   ✓ 发布模块")
        print("   ✓ 任务调度模块")
        
        # 测试发布器配置
        publisher = ToutiaoPublisher()
        if publisher.is_configured():
            print("   ✓ 头条号发布器已配置")
        else:
            print("   ⚠️  头条号发布器未配置")
            print("   💡 请在.env文件中配置头条号API信息")
            
    except Exception as e:
        print(f"   ✗ 模块导入失败: {e}")
        return False
    
    # 5. 测试目录结构
    print("\n5. 检查目录结构...")
    required_dirs = ['data', 'logs', 'data/videos']
    for dir_path in required_dirs:
        path = Path(dir_path)
        if path.exists():
            print(f"   ✓ {dir_path}/")
        else:
            path.mkdir(parents=True, exist_ok=True)
            print(f"   ✓ {dir_path}/ (已创建)")
    
    print("\n🎉 系统测试完成！")
    print("\n📋 下一步操作:")
    print("1. 编辑 .env 文件，配置API密钥")
    print("2. 运行 ./start_conda.sh 启动完整系统")
    print("3. 查看 logs/app.log 监控运行状态")
    
    return True

def main():
    """主函数"""
    try:
        result = asyncio.run(test_system())
        if result:
            print("\n✅ 系统就绪，可以开始使用！")
        else:
            print("\n❌ 系统测试失败，请检查错误信息")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
