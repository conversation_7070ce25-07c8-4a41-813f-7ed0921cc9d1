###
 # @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 # @Date: 2025-06-18 22:17:06
 # @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 # @LastEditTime: 2025-06-19 21:28:26
 # @FilePath: /makeMoneyPlan/.env.example
 # @Description: 
 # 
 # Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
### 
# 环境配置示例文件
# 复制此文件为 .env 并填入实际的API密钥

# 基础配置
DEBUG=false
ENVIRONMENT=production
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///./data/app.db

# AI服务API密钥
OPENAI_API_KEY=sk-5e5dfb52d25048baac63e3f106454683
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 头条号API配置
TOUTIAO_APP_ID=your_toutiao_app_id
TOUTIAO_APP_SECRET=your_toutiao_app_secret
TOUTIAO_ACCESS_TOKEN=your_toutiao_access_token

# 新闻API配置
BAIDU_API_KEY=https://api.aa1.cn/doc/baidu-rs.html
WEIBO_API_KEY=https://api.aa1.cn/doc/weibo-rs.html
ZHIHU_API_KEY=your_zhihu_api_key

# 定时任务配置
SCHEDULE_INTERVAL_HOURS=3
MAX_ARTICLES_PER_RUN=5

# 内容生成配置
MIN_STORY_LENGTH=200
MAX_STORY_LENGTH=2000
VIDEO_DURATION_SECONDS=60

# 文件路径配置
DATA_DIR=./data
LOGS_DIR=./logs
TEMP_DIR=./temp

# Redis配置（可选）
REDIS_URL=redis://localhost:6379

# 监控配置
ENABLE_MONITORING=true
