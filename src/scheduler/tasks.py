"""
定时任务调度器
"""
import asyncio
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger

from src.core.news_crawler import NewsCrawler
from src.core.content_extractor import StoryExtractor
from src.core.story_rewriter import StoryRewriter
from src.core.video_generator import VideoGenerator
from src.core.publisher import ToutiaoPublisher
from src.utils.database import get_db_manager
from src.utils.logger import get_logger, log_task_start, log_task_end
from src.config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()
db_manager = get_db_manager()


class ContentCreationPipeline:
    """内容创作流水线"""
    
    def __init__(self):
        self.news_crawler = NewsCrawler()
        self.story_extractor = StoryExtractor()
        self.story_rewriter = StoryRewriter()
        self.video_generator = VideoGenerator()
        self.publisher = ToutiaoPublisher()
        
    async def execute_full_pipeline(self) -> Dict[str, Any]:
        """执行完整的内容创作流水线"""
        task_id = f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()
        
        log_task_start("内容创作流水线", task_id)
        
        stats = {
            "articles_processed": 0,
            "stories_extracted": 0,
            "videos_generated": 0,
            "videos_published": 0,
            "errors": []
        }
        
        try:
            # 1. 搜索热点文章
            logger.info("开始搜索热点文章...")
            articles = await self.news_crawler.fetch_all_hot_articles(
                limit_per_source=settings.max_articles_per_run
            )
            
            if not articles:
                logger.warning("未获取到热点文章")
                return stats
            
            # 保存文章到数据库
            for article in articles:
                article_data = {
                    "title": article.title,
                    "content": article.content,
                    "url": article.url,
                    "source": article.source,
                    "publish_time": article.publish_time,
                    "author": article.author,
                    "tags": article.tags,
                    "hot_score": article.hot_score
                }
                db_manager.save_news_article(article_data)
            
            stats["articles_processed"] = len(articles)
            logger.info(f"获取到{len(articles)}篇热点文章")
            
            # 2. 丰富文章内容
            logger.info("开始丰富文章内容...")
            enriched_articles = await self.news_crawler.enrich_articles_content(articles)
            
            # 3. 筛选故事性文章
            story_articles = self.news_crawler.filter_story_articles(enriched_articles)
            
            if not story_articles:
                logger.warning("未找到故事性文章")
                return stats
            
            # 4. 提取故事内容
            logger.info("开始提取故事内容...")
            extracted_stories = await self.story_extractor.batch_extract_stories(story_articles)
            
            if not extracted_stories:
                logger.warning("未提取到有效故事")
                return stats
            
            # 保存提取的故事
            for story in extracted_stories:
                story_data = story.to_dict()
                db_manager.save_extracted_story(story_data)
            
            stats["stories_extracted"] = len(extracted_stories)
            logger.info(f"提取到{len(extracted_stories)}个故事")
            
            # 5. 筛选高质量故事
            high_quality_stories = self.story_extractor.filter_high_quality_stories(extracted_stories)
            
            if not high_quality_stories:
                logger.warning("未找到高质量故事")
                return stats
            
            # 6. 改写故事
            logger.info("开始改写故事...")
            rewritten_stories = await self.story_rewriter.batch_rewrite_stories(
                high_quality_stories[:3]  # 限制处理数量
            )
            
            if not rewritten_stories:
                logger.warning("故事改写失败")
                return stats
            
            # 保存改写的故事
            for story in rewritten_stories:
                story_data = story.to_dict()
                db_manager.save_rewritten_story(story_data)
            
            # 7. 生成视频
            logger.info("开始生成视频...")
            generated_videos = []
            
            for story in rewritten_stories:
                try:
                    video = await self.video_generator.generate_video_from_story(story)
                    if video:
                        generated_videos.append(video)
                        
                        # 保存视频记录
                        video_data = video.to_dict()
                        db_manager.save_generated_video(video_data)
                        
                except Exception as e:
                    logger.error(f"生成视频失败 {story.title}: {e}")
                    stats["errors"].append(f"视频生成失败: {story.title} - {e}")
            
            stats["videos_generated"] = len(generated_videos)
            logger.info(f"生成了{len(generated_videos)}个视频")
            
            # 8. 发布视频
            if generated_videos and self.publisher.is_configured():
                logger.info("开始发布视频...")
                
                publish_results = await self.publisher.batch_publish_videos(generated_videos)
                
                # 保存发布结果
                for result in publish_results:
                    result_data = result.to_dict()
                    db_manager.save_publish_result(result_data)
                    
                    if result.success:
                        stats["videos_published"] += 1
                    else:
                        stats["errors"].append(f"发布失败: {result.error_message}")
                
                logger.info(f"发布了{stats['videos_published']}个视频")
            else:
                logger.warning("跳过视频发布：未配置发布器或无视频")
            
            log_task_end("内容创作流水线", task_id, True)
            
        except Exception as e:
            error_msg = f"流水线执行失败: {e}"
            logger.error(error_msg)
            stats["errors"].append(error_msg)
            log_task_end("内容创作流水线", task_id, False, str(e))
        
        finally:
            # 保存任务执行记录
            end_time = datetime.now()
            task_data = {
                "task_name": "内容创作流水线",
                "start_time": start_time,
                "end_time": end_time,
                "success": len(stats["errors"]) == 0,
                "error_message": "; ".join(stats["errors"]) if stats["errors"] else None,
                "articles_processed": stats["articles_processed"],
                "stories_extracted": stats["stories_extracted"],
                "videos_generated": stats["videos_generated"],
                "videos_published": stats["videos_published"]
            }
            db_manager.save_task_execution(task_data)
        
        return stats


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        pass
    
    async def check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "status": "healthy",
            "checks": {}
        }
        
        try:
            # 检查数据库连接
            try:
                stats = db_manager.get_statistics()
                health_status["checks"]["database"] = {
                    "status": "ok",
                    "stats": stats
                }
            except Exception as e:
                health_status["checks"]["database"] = {
                    "status": "error",
                    "error": str(e)
                }
                health_status["status"] = "unhealthy"
            
            # 检查AI服务
            from src.utils.ai_client import get_ai_client
            ai_client = get_ai_client()
            available_providers = ai_client.get_available_providers()
            
            health_status["checks"]["ai_services"] = {
                "status": "ok" if available_providers else "warning",
                "available_providers": [p.value for p in available_providers]
            }
            
            # 检查发布器配置
            from src.core.publisher import ToutiaoPublisher
            publisher = ToutiaoPublisher()
            
            health_status["checks"]["publisher"] = {
                "status": "ok" if publisher.is_configured() else "warning",
                "configured": publisher.is_configured()
            }
            
            # 检查磁盘空间
            import shutil
            disk_usage = shutil.disk_usage(settings.data_dir)
            free_space_gb = disk_usage.free / (1024**3)
            
            health_status["checks"]["disk_space"] = {
                "status": "ok" if free_space_gb > 1 else "warning",
                "free_space_gb": round(free_space_gb, 2)
            }
            
        except Exception as e:
            health_status["status"] = "error"
            health_status["error"] = str(e)
        
        return health_status
    
    async def cleanup_old_files(self):
        """清理旧文件"""
        try:
            import os
            from pathlib import Path
            
            # 清理临时文件
            temp_dir = Path(settings.temp_dir)
            if temp_dir.exists():
                for file in temp_dir.glob("*"):
                    if file.is_file() and (datetime.now() - datetime.fromtimestamp(file.stat().st_mtime)).days > 1:
                        file.unlink()
                        logger.debug(f"删除临时文件: {file}")
            
            # 清理旧日志文件（保留30天）
            logs_dir = Path(settings.logs_dir)
            if logs_dir.exists():
                for log_file in logs_dir.glob("*.log.*"):
                    if (datetime.now() - datetime.fromtimestamp(log_file.stat().st_mtime)).days > 30:
                        log_file.unlink()
                        logger.debug(f"删除旧日志: {log_file}")
            
            logger.info("文件清理完成")
            
        except Exception as e:
            logger.error(f"文件清理失败: {e}")


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.pipeline = ContentCreationPipeline()
        self.monitor = SystemMonitor()
        
    async def start(self):
        """启动调度器"""
        try:
            # 添加主要任务：每3小时执行一次内容创作流水线
            self.scheduler.add_job(
                self.pipeline.execute_full_pipeline,
                trigger=IntervalTrigger(hours=settings.schedule_interval_hours),
                id="content_creation_pipeline",
                name="内容创作流水线",
                max_instances=1,  # 防止重复执行
                coalesce=True,    # 合并错过的执行
                misfire_grace_time=300  # 5分钟的容错时间
            )
            
            # 添加系统监控任务：每30分钟检查一次
            self.scheduler.add_job(
                self.monitor.check_system_health,
                trigger=IntervalTrigger(minutes=30),
                id="system_health_check",
                name="系统健康检查",
                max_instances=1
            )
            
            # 添加清理任务：每天凌晨2点执行
            self.scheduler.add_job(
                self.monitor.cleanup_old_files,
                trigger=CronTrigger(hour=2, minute=0),
                id="cleanup_old_files",
                name="清理旧文件",
                max_instances=1
            )
            
            # 启动调度器
            self.scheduler.start()
            logger.info("任务调度器启动成功")
            
            # 立即执行一次健康检查
            await self.monitor.check_system_health()
            
        except Exception as e:
            logger.error(f"启动任务调度器失败: {e}")
            raise
    
    async def stop(self):
        """停止调度器"""
        try:
            if self.scheduler.running:
                self.scheduler.shutdown(wait=True)
                logger.info("任务调度器已停止")
        except Exception as e:
            logger.error(f"停止任务调度器失败: {e}")
    
    def get_job_status(self) -> List[Dict[str, Any]]:
        """获取任务状态"""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        return jobs
    
    async def run_pipeline_now(self) -> Dict[str, Any]:
        """立即执行一次流水线"""
        logger.info("手动触发内容创作流水线")
        return await self.pipeline.execute_full_pipeline()
