"""
系统配置管理
"""
import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    app_name: str = "自动化内容创作系统"
    version: str = "1.0.0"
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # 数据库配置
    database_url: str = Field(default="sqlite:///./data/app.db", env="DATABASE_URL")
    
    # AI服务配置
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    
    # 头条号API配置
    toutiao_app_id: Optional[str] = Field(default=None, env="TOUTIAO_APP_ID")
    toutiao_app_secret: Optional[str] = Field(default=None, env="TOUTIAO_APP_SECRET")
    toutiao_access_token: Optional[str] = Field(default=None, env="TOUTIAO_ACCESS_TOKEN")
    
    # 新闻API配置
    baidu_api_key: Optional[str] = Field(default=None, env="BAIDU_API_KEY")
    weibo_api_key: Optional[str] = Field(default=None, env="WEIBO_API_KEY")
    zhihu_api_key: Optional[str] = Field(default=None, env="ZHIHU_API_KEY")
    
    # 定时任务配置
    schedule_interval_hours: int = Field(default=3, env="SCHEDULE_INTERVAL_HOURS")
    max_articles_per_run: int = Field(default=5, env="MAX_ARTICLES_PER_RUN")
    
    # 内容生成配置
    min_story_length: int = Field(default=200, env="MIN_STORY_LENGTH")
    max_story_length: int = Field(default=2000, env="MAX_STORY_LENGTH")
    video_duration_seconds: int = Field(default=60, env="VIDEO_DURATION_SECONDS")
    
    # 文件路径配置
    data_dir: str = Field(default="./data", env="DATA_DIR")
    logs_dir: str = Field(default="./logs", env="LOGS_DIR")
    temp_dir: str = Field(default="./temp", env="TEMP_DIR")
    
    # Redis配置（可选）
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    
    # 监控配置
    enable_monitoring: bool = Field(default=True, env="ENABLE_MONITORING")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
