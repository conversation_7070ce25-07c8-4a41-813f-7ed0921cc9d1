"""
动物角色转换和故事润色模块
将人物故事改写为动物角色版本，并进行内容润色
"""
import re
import json
import random
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass
from datetime import datetime

from src.core.content_extractor import ExtractedStory
from src.utils.ai_client import get_ai_client, AIProvider
from src.utils.logger import get_logger
from src.config.prompts import (
    ANIMAL_CHARACTER_PROMPT, 
    STORY_POLISH_PROMPT,
    VIDEO_TITLE_PROMPT,
    VIDEO_TAGS_PROMPT
)
from src.config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()
ai_client = get_ai_client()


@dataclass
class AnimalCharacter:
    """动物角色映射"""
    original_name: str
    animal_type: str
    animal_name: str
    personality_traits: List[str]
    
    def __str__(self):
        return f"{self.original_name} -> {self.animal_name}({self.animal_type})"


@dataclass
class RewrittenStory:
    """改写后的故事"""
    title: str
    content: str
    animal_characters: List[AnimalCharacter]
    theme: str
    original_story: ExtractedStory
    rewrite_time: datetime
    video_titles: List[str] = None
    video_tags: List[str] = None
    
    def __post_init__(self):
        if self.video_titles is None:
            self.video_titles = []
        if self.video_tags is None:
            self.video_tags = []
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "title": self.title,
            "content": self.content,
            "theme": self.theme,
            "animal_characters": [
                {
                    "original_name": char.original_name,
                    "animal_type": char.animal_type,
                    "animal_name": char.animal_name,
                    "personality_traits": char.personality_traits
                }
                for char in self.animal_characters
            ],
            "video_titles": self.video_titles,
            "video_tags": self.video_tags,
            "rewrite_time": self.rewrite_time.isoformat(),
            "original_story": self.original_story.to_dict()
        }


class AnimalCharacterMapper:
    """动物角色映射器"""
    
    # 预定义的动物角色库
    ANIMAL_TYPES = {
        "聪明": ["狐狸", "猫头鹰", "海豚", "乌鸦"],
        "勇敢": ["狮子", "老鹰", "狼", "老虎"],
        "善良": ["兔子", "小鹿", "绵羊", "熊猫"],
        "狡猾": ["蛇", "黄鼠狼", "狐狸", "鬣狗"],
        "勤劳": ["蜜蜂", "蚂蚁", "海狸", "啄木鸟"],
        "温和": ["小羊", "鸽子", "金鱼", "天鹅"],
        "强壮": ["大象", "犀牛", "熊", "野牛"],
        "机灵": ["猴子", "松鼠", "小鸟", "老鼠"],
        "忠诚": ["狗", "马", "企鹅", "大雁"],
        "优雅": ["天鹅", "孔雀", "鹤", "猫"]
    }
    
    def map_character_to_animal(self, character_name: str, traits: List[str]) -> AnimalCharacter:
        """将人物角色映射为动物"""
        # 根据性格特征选择合适的动物
        suitable_animals = []
        
        for trait in traits:
            for animal_trait, animals in self.ANIMAL_TYPES.items():
                if trait in animal_trait or animal_trait in trait:
                    suitable_animals.extend(animals)
        
        # 如果没有匹配的特征，随机选择
        if not suitable_animals:
            suitable_animals = ["小狗", "小猫", "小兔", "小鸟", "小鹿"]
        
        # 选择动物类型
        animal_type = random.choice(suitable_animals)
        
        # 生成动物名字
        animal_name = self._generate_animal_name(animal_type)
        
        return AnimalCharacter(
            original_name=character_name,
            animal_type=animal_type,
            animal_name=animal_name,
            personality_traits=traits
        )
    
    def _generate_animal_name(self, animal_type: str) -> str:
        """生成动物名字"""
        prefixes = ["小", "大", "老", ""]
        suffixes = ["", "儿", "子", "宝"]
        
        # 一些特殊的名字模式
        special_names = {
            "狐狸": ["小狐", "狐小七", "阿狐"],
            "兔子": ["小白", "兔宝宝", "跳跳"],
            "猫": ["小咪", "喵喵", "咪咪"],
            "狗": ["小汪", "旺财", "大黄"],
            "鸟": ["小鸣", "啾啾", "飞飞"],
            "熊": ["小熊", "熊大", "憨憨"]
        }
        
        if animal_type in special_names:
            return random.choice(special_names[animal_type])
        
        prefix = random.choice(prefixes)
        suffix = random.choice(suffixes)
        return f"{prefix}{animal_type}{suffix}"


class StoryRewriter:
    """故事改写器"""
    
    def __init__(self):
        self.ai_client = ai_client
        self.character_mapper = AnimalCharacterMapper()
    
    async def rewrite_story_with_animals(self, story: ExtractedStory) -> Optional[RewrittenStory]:
        """将故事改写为动物角色版本"""
        try:
            logger.info(f"开始改写故事: {story.title}")
            
            # 使用AI进行动物角色转换
            rewrite_result = await self._ai_rewrite_story(story)
            if not rewrite_result:
                logger.error(f"AI改写失败: {story.title}")
                return None
            
            # 解析改写结果
            animal_characters = self._parse_character_mapping(
                rewrite_result.get("character_mapping", "")
            )
            
            rewritten_story = RewrittenStory(
                title=story.title,  # 暂时使用原标题
                content=rewrite_result.get("rewritten_story", ""),
                animal_characters=animal_characters,
                theme=rewrite_result.get("theme", ""),
                original_story=story,
                rewrite_time=datetime.now()
            )
            
            # 润色故事内容
            polished_content = await self._polish_story(rewritten_story.content)
            if polished_content:
                rewritten_story.content = polished_content
            
            # 生成视频标题和标签
            await self._generate_video_metadata(rewritten_story)
            
            logger.info(f"故事改写完成: {rewritten_story.title}")
            return rewritten_story
            
        except Exception as e:
            logger.error(f"改写故事失败 {story.title}: {e}")
            return None
    
    async def _ai_rewrite_story(self, story: ExtractedStory) -> Optional[Dict[str, Any]]:
        """使用AI改写故事"""
        prompt = ANIMAL_CHARACTER_PROMPT.format(story=story.content)
        
        providers = self.ai_client.get_available_providers()
        
        for provider in providers:
            try:
                response = await self.ai_client.simple_completion(
                    prompt=prompt,
                    provider=provider,
                    temperature=0.8,  # 较高温度增加创意性
                    max_tokens=2000
                )
                
                if response:
                    return self._parse_rewrite_response(response)
                    
            except Exception as e:
                logger.error(f"AI改写失败 ({provider.value}): {e}")
                continue
        
        return None
    
    def _parse_rewrite_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析AI改写回复"""
        try:
            patterns = {
                "character_mapping": r"1\.\s*角色映射：\s*([^\n]+(?:\n[^\n]*)*?)(?=\n2\.|\n\n|\Z)",
                "rewritten_story": r"2\.\s*改写后的故事：\s*(.*?)(?=\n3\.|\n\n|\Z)",
                "theme": r"3\.\s*故事主题：\s*([^\n]+)"
            }
            
            result = {}
            for key, pattern in patterns.items():
                match = re.search(pattern, response, re.DOTALL)
                if match:
                    result[key] = match.group(1).strip()
                else:
                    result[key] = ""
            
            return result
            
        except Exception as e:
            logger.error(f"解析改写回复失败: {e}")
            return None
    
    def _parse_character_mapping(self, mapping_text: str) -> List[AnimalCharacter]:
        """解析角色映射"""
        characters = []
        
        # 解析格式如: "张三 -> 小狐狸(狐狸)"
        mapping_pattern = r"([^->\n]+)\s*->\s*([^(\n]+)\(([^)\n]+)\)"
        matches = re.findall(mapping_pattern, mapping_text)
        
        for match in matches:
            original_name = match[0].strip()
            animal_name = match[1].strip()
            animal_type = match[2].strip()
            
            character = AnimalCharacter(
                original_name=original_name,
                animal_type=animal_type,
                animal_name=animal_name,
                personality_traits=[]  # 可以后续补充
            )
            characters.append(character)
        
        return characters
    
    async def _polish_story(self, story_content: str) -> Optional[str]:
        """润色故事内容"""
        prompt = STORY_POLISH_PROMPT.format(story=story_content)
        
        providers = self.ai_client.get_available_providers()
        
        for provider in providers:
            try:
                response = await self.ai_client.simple_completion(
                    prompt=prompt,
                    provider=provider,
                    temperature=0.7,
                    max_tokens=1500
                )
                
                if response and len(response) > 100:
                    return response.strip()
                    
            except Exception as e:
                logger.error(f"故事润色失败 ({provider.value}): {e}")
                continue
        
        return None
    
    async def _generate_video_metadata(self, story: RewrittenStory):
        """生成视频标题和标签"""
        # 生成标题
        title_prompt = VIDEO_TITLE_PROMPT.format(story=story.content)
        titles_response = await self.ai_client.simple_completion(
            title_prompt, temperature=0.8, max_tokens=500
        )
        
        if titles_response:
            story.video_titles = self._parse_video_titles(titles_response)
        
        # 生成标签
        tags_prompt = VIDEO_TAGS_PROMPT.format(story=story.content)
        tags_response = await self.ai_client.simple_completion(
            tags_prompt, temperature=0.6, max_tokens=300
        )
        
        if tags_response:
            story.video_tags = self._parse_video_tags(tags_response)
    
    def _parse_video_titles(self, response: str) -> List[str]:
        """解析视频标题"""
        titles = []
        
        # 匹配格式如: "1. 悬念型：标题内容"
        pattern = r"\d+\.\s*[^：:]+[：:]\s*([^\n]+)"
        matches = re.findall(pattern, response)
        
        for match in matches:
            title = match.strip()
            if title and len(title) <= 30:  # 限制标题长度
                titles.append(title)
        
        return titles[:5]  # 最多返回5个标题
    
    def _parse_video_tags(self, response: str) -> List[str]:
        """解析视频标签"""
        tags = []
        
        # 提取所有可能的标签
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith(('请', '标签', '要求')):
                # 移除序号和特殊字符
                clean_line = re.sub(r'^\d+\.\s*', '', line)
                clean_line = re.sub(r'[#、，,]', ' ', clean_line)
                
                # 分割并清理标签
                line_tags = [tag.strip() for tag in clean_line.split() if tag.strip()]
                tags.extend(line_tags)
        
        # 去重并限制数量
        unique_tags = list(dict.fromkeys(tags))  # 保持顺序的去重
        return unique_tags[:15]  # 最多返回15个标签
    
    async def batch_rewrite_stories(self, stories: List[ExtractedStory]) -> List[RewrittenStory]:
        """批量改写故事"""
        logger.info(f"开始批量改写{len(stories)}个故事")
        
        rewritten_stories = []
        
        # 限制并发数量
        import asyncio
        semaphore = asyncio.Semaphore(2)
        
        async def rewrite_single(story: ExtractedStory) -> Optional[RewrittenStory]:
            async with semaphore:
                return await self.rewrite_story_with_animals(story)
        
        tasks = [rewrite_single(story) for story in stories]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"改写故事失败 {stories[i].title}: {result}")
            elif result:
                rewritten_stories.append(result)
        
        logger.info(f"成功改写{len(rewritten_stories)}个故事")
        return rewritten_stories
