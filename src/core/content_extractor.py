"""
故事内容提取和处理模块
使用AI识别和提取文章中的故事内容
"""
import re
import json
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from datetime import datetime

from src.core.news_crawler import NewsArticle
from src.utils.ai_client import get_ai_client, AIProvider
from src.utils.logger import get_logger
from src.config.prompts import STORY_EXTRACTION_PROMPT, CONTENT_QUALITY_PROMPT
from src.config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()
ai_client = get_ai_client()


@dataclass
class ExtractedStory:
    """提取的故事数据结构"""
    title: str
    content: str
    story_type: str
    main_characters: List[str]
    summary: str
    source_article: NewsArticle
    extraction_time: datetime
    quality_score: float = 0.0
    is_suitable: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "title": self.title,
            "content": self.content,
            "story_type": self.story_type,
            "main_characters": self.main_characters,
            "summary": self.summary,
            "quality_score": self.quality_score,
            "is_suitable": self.is_suitable,
            "extraction_time": self.extraction_time.isoformat(),
            "source": {
                "title": self.source_article.title,
                "url": self.source_article.url,
                "source": self.source_article.source
            }
        }


class StoryExtractor:
    """故事内容提取器"""
    
    def __init__(self):
        self.ai_client = ai_client
        
    async def extract_story_from_article(self, article: NewsArticle) -> Optional[ExtractedStory]:
        """从文章中提取故事内容"""
        if not article.content or len(article.content) < settings.min_story_length:
            logger.debug(f"文章内容太短，跳过: {article.title}")
            return None
            
        try:
            # 使用AI提取故事内容
            story_data = await self._ai_extract_story(article.content)
            if not story_data or not story_data.get("has_story"):
                logger.debug(f"文章不包含故事内容: {article.title}")
                return None
                
            # 创建提取的故事对象
            extracted_story = ExtractedStory(
                title=story_data.get("title", article.title),
                content=story_data.get("story_content", ""),
                story_type=story_data.get("story_type", "未知"),
                main_characters=story_data.get("main_characters", []),
                summary=story_data.get("summary", ""),
                source_article=article,
                extraction_time=datetime.now()
            )
            
            # 验证故事内容质量
            if await self._validate_story_content(extracted_story):
                logger.info(f"成功提取故事: {extracted_story.title}")
                return extracted_story
            else:
                logger.debug(f"故事内容质量不符合要求: {extracted_story.title}")
                return None
                
        except Exception as e:
            logger.error(f"提取故事内容失败 {article.title}: {e}")
            return None
    
    async def _ai_extract_story(self, content: str) -> Optional[Dict[str, Any]]:
        """使用AI提取故事内容"""
        prompt = STORY_EXTRACTION_PROMPT.format(content=content[:3000])  # 限制输入长度
        
        # 尝试多个AI提供商
        providers = self.ai_client.get_available_providers()
        
        for provider in providers:
            try:
                response = await self.ai_client.simple_completion(
                    prompt=prompt,
                    provider=provider,
                    temperature=0.3,  # 较低温度确保一致性
                    max_tokens=2000
                )
                
                if response:
                    return self._parse_story_response(response)
                    
            except Exception as e:
                logger.error(f"AI提取失败 ({provider.value}): {e}")
                continue
        
        return None
    
    def _parse_story_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析AI回复的故事内容"""
        try:
            # 使用正则表达式解析结构化回复
            patterns = {
                "has_story": r"1\.\s*是否包含故事：\s*([是否])",
                "story_type": r"2\.\s*故事类型：\s*([^\n]+)",
                "main_characters": r"3\.\s*主要人物：\s*([^\n]+)",
                "summary": r"4\.\s*故事梗概：\s*([^\n]+)",
                "story_content": r"5\.\s*完整故事：\s*(.*?)(?=\n\n|\Z)"
            }
            
            result = {}
            for key, pattern in patterns.items():
                match = re.search(pattern, response, re.DOTALL)
                if match:
                    value = match.group(1).strip()
                    if key == "has_story":
                        result[key] = value == "是"
                    elif key == "main_characters":
                        # 解析人物列表
                        characters = [char.strip() for char in value.split("、") if char.strip()]
                        result[key] = characters
                    else:
                        result[key] = value
                else:
                    if key == "has_story":
                        result[key] = False
                    elif key == "main_characters":
                        result[key] = []
                    else:
                        result[key] = ""
            
            return result
            
        except Exception as e:
            logger.error(f"解析AI回复失败: {e}")
            return None
    
    async def _validate_story_content(self, story: ExtractedStory) -> bool:
        """验证故事内容质量"""
        # 基础验证
        if not story.content or len(story.content) < settings.min_story_length:
            return False
            
        if len(story.content) > settings.max_story_length:
            return False
            
        # 检查是否包含必要的故事元素
        story_indicators = [
            "故事", "经历", "发生", "遇到", "经过", "结果", 
            "开始", "然后", "最后", "后来", "接着"
        ]
        
        content_lower = story.content.lower()
        indicator_count = sum(1 for indicator in story_indicators if indicator in content_lower)
        
        if indicator_count < 2:  # 至少包含2个故事指示词
            return False
        
        # 使用AI进行质量评估
        try:
            quality_score = await self._ai_quality_assessment(story)
            story.quality_score = quality_score
            story.is_suitable = quality_score >= 6.0  # 6分以上认为合适
            
            return story.is_suitable
            
        except Exception as e:
            logger.error(f"AI质量评估失败: {e}")
            # 如果AI评估失败，使用基础验证结果
            story.quality_score = 5.0
            story.is_suitable = True
            return True
    
    async def _ai_quality_assessment(self, story: ExtractedStory) -> float:
        """使用AI评估故事质量"""
        prompt = CONTENT_QUALITY_PROMPT.format(story=story.content)
        
        providers = self.ai_client.get_available_providers()
        
        for provider in providers:
            try:
                response = await self.ai_client.simple_completion(
                    prompt=prompt,
                    provider=provider,
                    temperature=0.2,
                    max_tokens=1000
                )
                
                if response:
                    # 从回复中提取总体评分
                    score_match = re.search(r"总体评分[：:]\s*(\d+(?:\.\d+)?)", response)
                    if score_match:
                        return float(score_match.group(1))
                        
            except Exception as e:
                logger.error(f"AI质量评估失败 ({provider.value}): {e}")
                continue
        
        return 5.0  # 默认分数
    
    async def batch_extract_stories(self, articles: List[NewsArticle]) -> List[ExtractedStory]:
        """批量提取故事内容"""
        logger.info(f"开始批量提取{len(articles)}篇文章的故事内容")
        
        extracted_stories = []
        
        # 限制并发数量以避免API限制
        semaphore = asyncio.Semaphore(3)
        
        async def extract_single(article: NewsArticle) -> Optional[ExtractedStory]:
            async with semaphore:
                return await self.extract_story_from_article(article)
        
        import asyncio
        tasks = [extract_single(article) for article in articles]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"提取故事失败 {articles[i].title}: {result}")
            elif result:
                extracted_stories.append(result)
        
        logger.info(f"成功提取{len(extracted_stories)}个故事")
        return extracted_stories
    
    def filter_high_quality_stories(self, stories: List[ExtractedStory]) -> List[ExtractedStory]:
        """筛选高质量故事"""
        high_quality_stories = [
            story for story in stories 
            if story.is_suitable and story.quality_score >= 6.0
        ]
        
        # 按质量分数排序
        high_quality_stories.sort(key=lambda x: x.quality_score, reverse=True)
        
        logger.info(f"筛选出{len(high_quality_stories)}个高质量故事")
        return high_quality_stories
