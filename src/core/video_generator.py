"""
视频生成模块
将文本故事转换为视频内容，包括配音、字幕和视觉效果
"""
import os
import asyncio
import tempfile
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass
from datetime import datetime
import json

# 音频和视频处理
from moviepy.editor import (
    VideoFileClip, AudioFileClip, TextClip, CompositeVideoClip,
    concatenate_videoclips, ColorClip
)
from pydub import AudioSegment
import edge_tts
from PIL import Image, ImageDraw, ImageFont

from src.core.story_rewriter import RewrittenStory
from src.utils.logger import get_logger
from src.config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


@dataclass
class VideoSegment:
    """视频片段"""
    text: str
    audio_file: str
    duration: float
    start_time: float = 0.0


@dataclass
class GeneratedVideo:
    """生成的视频"""
    video_file: str
    title: str
    description: str
    tags: List[str]
    duration: float
    story: RewrittenStory
    generation_time: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "video_file": self.video_file,
            "title": self.title,
            "description": self.description,
            "tags": self.tags,
            "duration": self.duration,
            "generation_time": self.generation_time.isoformat(),
            "story": self.story.to_dict()
        }


class TextToSpeechGenerator:
    """文本转语音生成器"""
    
    def __init__(self):
        # 支持的语音列表
        self.voices = [
            "zh-CN-XiaoxiaoNeural",  # 女声，温和
            "zh-CN-YunxiNeural",     # 男声，年轻
            "zh-CN-YunyangNeural",   # 男声，成熟
            "zh-CN-XiaoyiNeural",    # 女声，甜美
        ]
        
    async def generate_speech(self, text: str, output_file: str, voice: str = None) -> bool:
        """生成语音文件"""
        try:
            if not voice:
                voice = self.voices[0]  # 默认使用第一个语音
                
            # 使用edge-tts生成语音
            communicate = edge_tts.Communicate(text, voice)
            await communicate.save(output_file)
            
            logger.debug(f"语音生成成功: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"语音生成失败: {e}")
            return False
    
    async def batch_generate_speech(self, text_segments: List[str], output_dir: str) -> List[str]:
        """批量生成语音文件"""
        audio_files = []
        
        for i, text in enumerate(text_segments):
            output_file = os.path.join(output_dir, f"segment_{i:03d}.wav")
            success = await self.generate_speech(text, output_file)
            
            if success:
                audio_files.append(output_file)
            else:
                audio_files.append(None)
        
        return audio_files


class SubtitleGenerator:
    """字幕生成器"""
    
    def __init__(self):
        self.font_size = 24
        self.font_color = 'white'
        self.stroke_color = 'black'
        self.stroke_width = 2
        
    def create_subtitle_clip(self, text: str, duration: float, start_time: float = 0) -> TextClip:
        """创建字幕片段"""
        try:
            # 创建文本片段
            txt_clip = TextClip(
                text,
                fontsize=self.font_size,
                color=self.font_color,
                stroke_color=self.stroke_color,
                stroke_width=self.stroke_width,
                method='caption',
                size=(720, None),  # 限制宽度，高度自适应
                align='center'
            ).set_position(('center', 'bottom')).set_start(start_time).set_duration(duration)
            
            return txt_clip
            
        except Exception as e:
            logger.error(f"创建字幕失败: {e}")
            return None
    
    def split_text_for_subtitles(self, text: str, max_chars_per_line: int = 20) -> List[str]:
        """将长文本分割为适合字幕的短句"""
        sentences = text.replace('。', '。|').replace('！', '！|').replace('？', '？|').split('|')
        sentences = [s.strip() for s in sentences if s.strip()]
        
        subtitle_segments = []
        current_segment = ""
        
        for sentence in sentences:
            if len(current_segment + sentence) <= max_chars_per_line:
                current_segment += sentence
            else:
                if current_segment:
                    subtitle_segments.append(current_segment)
                current_segment = sentence
        
        if current_segment:
            subtitle_segments.append(current_segment)
        
        return subtitle_segments


class BackgroundGenerator:
    """背景视频生成器"""
    
    def __init__(self):
        self.background_colors = [
            (135, 206, 235),  # 天空蓝
            (144, 238, 144),  # 浅绿色
            (255, 182, 193),  # 浅粉色
            (221, 160, 221),  # 紫色
            (255, 218, 185),  # 桃色
        ]
        
    def create_background_video(self, duration: float, size: Tuple[int, int] = (720, 1280)) -> ColorClip:
        """创建背景视频"""
        try:
            # 随机选择背景颜色
            import random
            color = random.choice(self.background_colors)
            
            # 创建纯色背景
            background = ColorClip(size=size, color=color, duration=duration)
            
            return background
            
        except Exception as e:
            logger.error(f"创建背景视频失败: {e}")
            return None
    
    def create_animated_background(self, duration: float, size: Tuple[int, int] = (720, 1280)) -> Optional[VideoFileClip]:
        """创建动画背景（可选功能）"""
        # 这里可以添加更复杂的动画背景生成逻辑
        # 比如粒子效果、渐变动画等
        return self.create_background_video(duration, size)


class VideoGenerator:
    """视频生成器主类"""
    
    def __init__(self):
        self.tts_generator = TextToSpeechGenerator()
        self.subtitle_generator = SubtitleGenerator()
        self.background_generator = BackgroundGenerator()
        
        # 确保临时目录存在
        self.temp_dir = Path(settings.temp_dir)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
    async def generate_video_from_story(self, story: RewrittenStory) -> Optional[GeneratedVideo]:
        """从故事生成视频"""
        try:
            logger.info(f"开始生成视频: {story.title}")
            
            # 创建临时工作目录
            with tempfile.TemporaryDirectory(dir=self.temp_dir) as temp_work_dir:
                # 分割文本为语音片段
                text_segments = self._split_story_for_speech(story.content)
                
                # 生成语音文件
                audio_files = await self.tts_generator.batch_generate_speech(
                    text_segments, temp_work_dir
                )
                
                # 创建视频片段
                video_segments = await self._create_video_segments(
                    text_segments, audio_files, temp_work_dir
                )
                
                if not video_segments:
                    logger.error("创建视频片段失败")
                    return None
                
                # 合成最终视频
                final_video_path = await self._compose_final_video(
                    video_segments, story, temp_work_dir
                )
                
                if not final_video_path:
                    logger.error("合成最终视频失败")
                    return None
                
                # 移动到最终位置
                final_output_path = self._get_output_path(story)
                os.rename(final_video_path, final_output_path)
                
                # 创建生成的视频对象
                video_duration = self._get_video_duration(final_output_path)
                
                generated_video = GeneratedVideo(
                    video_file=final_output_path,
                    title=story.video_titles[0] if story.video_titles else story.title,
                    description=self._generate_video_description(story),
                    tags=story.video_tags,
                    duration=video_duration,
                    story=story,
                    generation_time=datetime.now()
                )
                
                logger.info(f"视频生成完成: {final_output_path}")
                return generated_video
                
        except Exception as e:
            logger.error(f"生成视频失败 {story.title}: {e}")
            return None
    
    def _split_story_for_speech(self, content: str) -> List[str]:
        """将故事内容分割为适合语音的片段"""
        # 按句子分割
        sentences = content.replace('。', '。|').replace('！', '！|').replace('？', '？|').split('|')
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # 合并短句，避免片段过短
        segments = []
        current_segment = ""
        
        for sentence in sentences:
            if len(current_segment + sentence) <= 100:  # 每段最多100字符
                current_segment += sentence
            else:
                if current_segment:
                    segments.append(current_segment)
                current_segment = sentence
        
        if current_segment:
            segments.append(current_segment)
        
        return segments
    
    async def _create_video_segments(
        self, 
        text_segments: List[str], 
        audio_files: List[str], 
        work_dir: str
    ) -> List[VideoSegment]:
        """创建视频片段"""
        video_segments = []
        current_time = 0.0
        
        for i, (text, audio_file) in enumerate(zip(text_segments, audio_files)):
            if not audio_file or not os.path.exists(audio_file):
                continue
                
            # 获取音频时长
            audio_duration = self._get_audio_duration(audio_file)
            
            segment = VideoSegment(
                text=text,
                audio_file=audio_file,
                duration=audio_duration,
                start_time=current_time
            )
            
            video_segments.append(segment)
            current_time += audio_duration
        
        return video_segments
    
    async def _compose_final_video(
        self, 
        segments: List[VideoSegment], 
        story: RewrittenStory, 
        work_dir: str
    ) -> Optional[str]:
        """合成最终视频"""
        try:
            # 计算总时长
            total_duration = sum(seg.duration for seg in segments)
            
            # 创建背景视频
            background = self.background_generator.create_background_video(total_duration)
            if not background:
                return None
            
            # 创建音频轨道
            audio_clips = []
            for segment in segments:
                audio_clip = AudioFileClip(segment.audio_file).set_start(segment.start_time)
                audio_clips.append(audio_clip)
            
            final_audio = concatenate_videoclips(audio_clips, method="compose")
            
            # 创建字幕
            subtitle_clips = []
            for segment in segments:
                subtitle_segments = self.subtitle_generator.split_text_for_subtitles(segment.text)
                segment_duration = segment.duration / len(subtitle_segments)
                
                for j, subtitle_text in enumerate(subtitle_segments):
                    start_time = segment.start_time + j * segment_duration
                    subtitle_clip = self.subtitle_generator.create_subtitle_clip(
                        subtitle_text, segment_duration, start_time
                    )
                    if subtitle_clip:
                        subtitle_clips.append(subtitle_clip)
            
            # 合成视频
            video_clips = [background] + subtitle_clips
            final_video = CompositeVideoClip(video_clips).set_audio(final_audio)
            
            # 输出文件
            output_file = os.path.join(work_dir, "final_video.mp4")
            final_video.write_videofile(
                output_file,
                fps=24,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True
            )
            
            # 清理资源
            final_video.close()
            final_audio.close()
            for clip in audio_clips:
                clip.close()
            for clip in subtitle_clips:
                if clip:
                    clip.close()
            
            return output_file
            
        except Exception as e:
            logger.error(f"合成视频失败: {e}")
            return None
    
    def _get_audio_duration(self, audio_file: str) -> float:
        """获取音频文件时长"""
        try:
            audio = AudioSegment.from_file(audio_file)
            return len(audio) / 1000.0  # 转换为秒
        except Exception as e:
            logger.error(f"获取音频时长失败: {e}")
            return 1.0  # 默认1秒
    
    def _get_video_duration(self, video_file: str) -> float:
        """获取视频文件时长"""
        try:
            video = VideoFileClip(video_file)
            duration = video.duration
            video.close()
            return duration
        except Exception as e:
            logger.error(f"获取视频时长失败: {e}")
            return 0.0
    
    def _get_output_path(self, story: RewrittenStory) -> str:
        """获取输出文件路径"""
        # 创建输出目录
        output_dir = Path(settings.data_dir) / "videos"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_title = "".join(c for c in story.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{timestamp}_{safe_title[:20]}.mp4"
        
        return str(output_dir / filename)
    
    def _generate_video_description(self, story: RewrittenStory) -> str:
        """生成视频描述"""
        description = f"{story.theme}\n\n"
        description += f"故事简介：{story.original_story.summary}\n\n"
        
        if story.animal_characters:
            description += "主要角色：\n"
            for char in story.animal_characters:
                description += f"• {char.animal_name}（{char.animal_type}）\n"
        
        description += "\n#动物故事 #正能量 #短视频"
        
        return description
