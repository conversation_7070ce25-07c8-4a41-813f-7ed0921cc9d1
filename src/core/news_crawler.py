"""
热点文章搜索模块
支持多个新闻源的热点内容抓取
"""
import asyncio
import aiohttp
import requests
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from dataclasses import dataclass
import json
import re

from src.utils.logger import get_logger, log_api_call
from src.config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


@dataclass
class NewsArticle:
    """新闻文章数据结构"""
    title: str
    content: str
    url: str
    source: str
    publish_time: Optional[datetime] = None
    author: Optional[str] = None
    tags: List[str] = None
    hot_score: int = 0
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class NewsSource:
    """新闻源基类"""
    
    def __init__(self, name: str, base_url: str):
        self.name = name
        self.base_url = base_url
        
    async def fetch_hot_articles(self, limit: int = 10) -> List[NewsArticle]:
        """获取热点文章"""
        raise NotImplementedError
        
    async def fetch_article_content(self, url: str) -> Optional[str]:
        """获取文章详细内容"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=30) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        return self._extract_content(soup)
        except Exception as e:
            logger.error(f"获取文章内容失败 {url}: {e}")
        return None
        
    def _extract_content(self, soup: BeautifulSoup) -> str:
        """从HTML中提取文章内容"""
        # 通用内容提取逻辑
        content_selectors = [
            'article', '.article-content', '.content', 
            '.post-content', '.entry-content', 'main'
        ]
        
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                # 移除脚本和样式
                for script in content_elem(["script", "style"]):
                    script.decompose()
                return content_elem.get_text(strip=True)
        
        # 如果没有找到特定选择器，尝试提取body内容
        body = soup.find('body')
        if body:
            for script in body(["script", "style", "nav", "header", "footer"]):
                script.decompose()
            return body.get_text(strip=True)
        
        return soup.get_text(strip=True)


class BaiduHotSource(NewsSource):
    """百度热搜新闻源"""
    
    def __init__(self):
        super().__init__("百度热搜", "https://top.baidu.com")
        
    async def fetch_hot_articles(self, limit: int = 10) -> List[NewsArticle]:
        """获取百度热搜文章"""
        articles = []
        try:
            url = f"{self.base_url}/board?tab=realtime"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        
                        # 解析热搜条目
                        items = soup.select('.category-wrap_iQLoo .c-single-text-ellipsis')[:limit]
                        
                        for i, item in enumerate(items):
                            title = item.get_text(strip=True)
                            if title:
                                article = NewsArticle(
                                    title=title,
                                    content="",  # 需要进一步获取
                                    url="",
                                    source=self.name,
                                    hot_score=limit - i,  # 排名越靠前分数越高
                                    publish_time=datetime.now()
                                )
                                articles.append(article)
                                
            log_api_call(self.name, url, response.status if 'response' in locals() else None)
            logger.info(f"从{self.name}获取到{len(articles)}篇文章")
            
        except Exception as e:
            logger.error(f"获取{self.name}热点文章失败: {e}")
            
        return articles


class WeiboHotSource(NewsSource):
    """微博热搜新闻源"""
    
    def __init__(self):
        super().__init__("微博热搜", "https://s.weibo.com")
        
    async def fetch_hot_articles(self, limit: int = 10) -> List[NewsArticle]:
        """获取微博热搜文章"""
        articles = []
        try:
            # 微博热搜API（需要实际的API密钥）
            url = f"{self.base_url}/top/summary"
            
            # 这里使用模拟数据，实际使用时需要配置真实的API
            mock_data = [
                {"title": "热点话题1", "hot_score": 1000000},
                {"title": "热点话题2", "hot_score": 800000},
                {"title": "热点话题3", "hot_score": 600000},
            ]
            
            for i, item in enumerate(mock_data[:limit]):
                article = NewsArticle(
                    title=item["title"],
                    content="",
                    url=f"{self.base_url}/weibo?q={item['title']}",
                    source=self.name,
                    hot_score=item["hot_score"],
                    publish_time=datetime.now()
                )
                articles.append(article)
                
            logger.info(f"从{self.name}获取到{len(articles)}篇文章")
            
        except Exception as e:
            logger.error(f"获取{self.name}热点文章失败: {e}")
            
        return articles


class ZhihuHotSource(NewsSource):
    """知乎热榜新闻源"""
    
    def __init__(self):
        super().__init__("知乎热榜", "https://www.zhihu.com")
        
    async def fetch_hot_articles(self, limit: int = 10) -> List[NewsArticle]:
        """获取知乎热榜文章"""
        articles = []
        try:
            url = f"{self.base_url}/api/v3/feed/topstory/hot-lists/total"
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        items = data.get('data', [])[:limit]
                        
                        for item in items:
                            target = item.get('target', {})
                            title = target.get('title', '')
                            question_id = target.get('id', '')
                            
                            if title:
                                article = NewsArticle(
                                    title=title,
                                    content="",
                                    url=f"{self.base_url}/question/{question_id}",
                                    source=self.name,
                                    hot_score=item.get('detail_text', '0').replace('万热度', ''),
                                    publish_time=datetime.now()
                                )
                                articles.append(article)
                                
            log_api_call(self.name, url, response.status if 'response' in locals() else None)
            logger.info(f"从{self.name}获取到{len(articles)}篇文章")
            
        except Exception as e:
            logger.error(f"获取{self.name}热点文章失败: {e}")
            
        return articles


class NewsCrawler:
    """新闻爬虫主类"""
    
    def __init__(self):
        self.sources = [
            BaiduHotSource(),
            WeiboHotSource(),
            ZhihuHotSource(),
        ]
        
    async def fetch_all_hot_articles(self, limit_per_source: int = 5) -> List[NewsArticle]:
        """从所有源获取热点文章"""
        all_articles = []
        
        tasks = []
        for source in self.sources:
            task = source.fetch_hot_articles(limit_per_source)
            tasks.append(task)
            
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"源 {self.sources[i].name} 获取失败: {result}")
            else:
                all_articles.extend(result)
                
        # 按热度排序
        all_articles.sort(key=lambda x: x.hot_score, reverse=True)
        
        logger.info(f"总共获取到{len(all_articles)}篇热点文章")
        return all_articles
        
    async def enrich_articles_content(self, articles: List[NewsArticle]) -> List[NewsArticle]:
        """丰富文章内容"""
        enriched_articles = []
        
        for article in articles:
            if article.url and not article.content:
                # 尝试获取完整内容
                for source in self.sources:
                    if source.name == article.source:
                        content = await source.fetch_article_content(article.url)
                        if content:
                            article.content = content
                            break
                            
            # 只保留有内容的文章
            if article.content and len(article.content) > 100:
                enriched_articles.append(article)
                
        logger.info(f"成功丰富{len(enriched_articles)}篇文章内容")
        return enriched_articles
        
    def filter_story_articles(self, articles: List[NewsArticle]) -> List[NewsArticle]:
        """筛选包含故事性内容的文章"""
        story_keywords = [
            '故事', '经历', '遭遇', '发生', '事件', '案例', 
            '真实', '亲历', '见证', '回忆', '往事', '传奇'
        ]
        
        story_articles = []
        for article in articles:
            # 检查标题和内容是否包含故事关键词
            text = f"{article.title} {article.content}".lower()
            if any(keyword in text for keyword in story_keywords):
                story_articles.append(article)
                
        logger.info(f"筛选出{len(story_articles)}篇故事性文章")
        return story_articles
