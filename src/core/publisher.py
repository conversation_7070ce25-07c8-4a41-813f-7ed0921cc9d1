"""
头条号自动发布模块
实现头条号API集成，自动上传视频和发布文章
"""
import os
import json
import time
import hashlib
import hmac
import base64
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from datetime import datetime
import aiohttp
import asyncio

from src.core.video_generator import GeneratedVideo
from src.utils.logger import get_logger, log_api_call
from src.config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


@dataclass
class PublishResult:
    """发布结果"""
    success: bool
    article_id: Optional[str] = None
    video_id: Optional[str] = None
    error_message: Optional[str] = None
    publish_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "success": self.success,
            "article_id": self.article_id,
            "video_id": self.video_id,
            "error_message": self.error_message,
            "publish_time": self.publish_time.isoformat() if self.publish_time else None
        }


class ToutiaoAPIClient:
    """头条号API客户端"""
    
    def __init__(self):
        self.app_id = settings.toutiao_app_id
        self.app_secret = settings.toutiao_app_secret
        self.access_token = settings.toutiao_access_token
        self.base_url = "https://mp.toutiao.com/mp/agw/article_api"
        
        if not all([self.app_id, self.app_secret]):
            logger.warning("头条号API配置不完整")
    
    def _generate_signature(self, params: Dict[str, Any], timestamp: str) -> str:
        """生成API签名"""
        try:
            # 按字典序排序参数
            sorted_params = sorted(params.items())
            query_string = "&".join([f"{k}={v}" for k, v in sorted_params])
            
            # 构建签名字符串
            sign_string = f"{query_string}&timestamp={timestamp}&app_secret={self.app_secret}"
            
            # 生成MD5签名
            signature = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
            
            return signature
            
        except Exception as e:
            logger.error(f"生成签名失败: {e}")
            return ""
    
    async def upload_video(self, video_file: str) -> Optional[str]:
        """上传视频文件"""
        try:
            if not os.path.exists(video_file):
                logger.error(f"视频文件不存在: {video_file}")
                return None
            
            url = f"{self.base_url}/upload_video/"
            timestamp = str(int(time.time()))
            
            # 准备上传参数
            params = {
                "app_id": self.app_id,
                "timestamp": timestamp,
            }
            
            # 生成签名
            signature = self._generate_signature(params, timestamp)
            params["signature"] = signature
            
            # 准备文件数据
            async with aiohttp.ClientSession() as session:
                with open(video_file, 'rb') as f:
                    data = aiohttp.FormData()
                    data.add_field('video', f, filename=os.path.basename(video_file))
                    
                    for key, value in params.items():
                        data.add_field(key, str(value))
                    
                    async with session.post(url, data=data, timeout=300) as response:
                        result = await response.json()
                        
                        log_api_call("头条号", "upload_video", response.status)
                        
                        if result.get("err_no") == 0:
                            video_id = result.get("data", {}).get("video_id")
                            logger.info(f"视频上传成功: {video_id}")
                            return video_id
                        else:
                            logger.error(f"视频上传失败: {result.get('err_msg')}")
                            return None
                            
        except Exception as e:
            logger.error(f"上传视频异常: {e}")
            return None
    
    async def publish_article(
        self, 
        title: str, 
        content: str, 
        video_id: Optional[str] = None,
        tags: List[str] = None,
        category: str = "其他"
    ) -> Optional[str]:
        """发布文章"""
        try:
            url = f"{self.base_url}/create/"
            timestamp = str(int(time.time()))
            
            # 准备发布参数
            article_data = {
                "app_id": self.app_id,
                "timestamp": timestamp,
                "title": title,
                "content": content,
                "category": category,
                "tag": ",".join(tags) if tags else "",
                "cover_images": [],
                "is_original": 1,  # 标记为原创
                "article_type": 0,  # 普通文章
            }
            
            # 如果有视频，添加视频ID
            if video_id:
                article_data["video_id"] = video_id
                article_data["article_type"] = 2  # 视频文章
            
            # 生成签名
            signature = self._generate_signature(article_data, timestamp)
            article_data["signature"] = signature
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=article_data, timeout=60) as response:
                    result = await response.json()
                    
                    log_api_call("头条号", "publish_article", response.status)
                    
                    if result.get("err_no") == 0:
                        article_id = result.get("data", {}).get("article_id")
                        logger.info(f"文章发布成功: {article_id}")
                        return article_id
                    else:
                        logger.error(f"文章发布失败: {result.get('err_msg')}")
                        return None
                        
        except Exception as e:
            logger.error(f"发布文章异常: {e}")
            return None
    
    async def get_article_status(self, article_id: str) -> Optional[Dict[str, Any]]:
        """获取文章状态"""
        try:
            url = f"{self.base_url}/article_detail/"
            timestamp = str(int(time.time()))
            
            params = {
                "app_id": self.app_id,
                "timestamp": timestamp,
                "article_id": article_id,
            }
            
            signature = self._generate_signature(params, timestamp)
            params["signature"] = signature
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=30) as response:
                    result = await response.json()
                    
                    if result.get("err_no") == 0:
                        return result.get("data")
                    else:
                        logger.error(f"获取文章状态失败: {result.get('err_msg')}")
                        return None
                        
        except Exception as e:
            logger.error(f"获取文章状态异常: {e}")
            return None


class ContentOptimizer:
    """内容优化器"""
    
    def __init__(self):
        pass
    
    def optimize_title(self, title: str, max_length: int = 30) -> str:
        """优化标题"""
        # 移除特殊字符
        cleaned_title = "".join(c for c in title if c.isalnum() or c in "，。！？、：；""''（）【】《》")
        
        # 限制长度
        if len(cleaned_title) > max_length:
            cleaned_title = cleaned_title[:max_length-1] + "…"
        
        return cleaned_title
    
    def optimize_tags(self, tags: List[str], max_tags: int = 5) -> List[str]:
        """优化标签"""
        # 过滤和清理标签
        cleaned_tags = []
        for tag in tags:
            cleaned_tag = tag.strip().replace("#", "")
            if cleaned_tag and len(cleaned_tag) <= 10:  # 标签长度限制
                cleaned_tags.append(cleaned_tag)
        
        # 去重并限制数量
        unique_tags = list(dict.fromkeys(cleaned_tags))
        return unique_tags[:max_tags]
    
    def format_content_for_platform(self, content: str) -> str:
        """为平台格式化内容"""
        # 添加段落分隔
        formatted_content = content.replace("。", "。\n\n")
        
        # 添加表情符号（可选）
        emoji_map = {
            "开心": "😊",
            "难过": "😢",
            "惊讶": "😲",
            "生气": "😠",
            "害怕": "😨"
        }
        
        for word, emoji in emoji_map.items():
            if word in formatted_content:
                formatted_content = formatted_content.replace(word, f"{word}{emoji}")
        
        return formatted_content


class ToutiaoPublisher:
    """头条号发布器"""
    
    def __init__(self):
        self.api_client = ToutiaoAPIClient()
        self.content_optimizer = ContentOptimizer()
    
    async def publish_video_story(self, video: GeneratedVideo) -> PublishResult:
        """发布视频故事"""
        try:
            logger.info(f"开始发布视频: {video.title}")
            
            # 检查API配置
            if not all([self.api_client.app_id, self.api_client.app_secret]):
                return PublishResult(
                    success=False,
                    error_message="头条号API配置不完整"
                )
            
            # 上传视频
            video_id = await self.api_client.upload_video(video.video_file)
            if not video_id:
                return PublishResult(
                    success=False,
                    error_message="视频上传失败"
                )
            
            # 优化内容
            optimized_title = self.content_optimizer.optimize_title(video.title)
            optimized_tags = self.content_optimizer.optimize_tags(video.tags)
            formatted_description = self.content_optimizer.format_content_for_platform(
                video.description
            )
            
            # 发布文章
            article_id = await self.api_client.publish_article(
                title=optimized_title,
                content=formatted_description,
                video_id=video_id,
                tags=optimized_tags,
                category="娱乐"
            )
            
            if article_id:
                result = PublishResult(
                    success=True,
                    article_id=article_id,
                    video_id=video_id,
                    publish_time=datetime.now()
                )
                logger.info(f"视频发布成功: 文章ID={article_id}, 视频ID={video_id}")
                return result
            else:
                return PublishResult(
                    success=False,
                    video_id=video_id,
                    error_message="文章发布失败"
                )
                
        except Exception as e:
            logger.error(f"发布视频失败: {e}")
            return PublishResult(
                success=False,
                error_message=str(e)
            )
    
    async def batch_publish_videos(self, videos: List[GeneratedVideo]) -> List[PublishResult]:
        """批量发布视频"""
        logger.info(f"开始批量发布{len(videos)}个视频")
        
        results = []
        
        # 限制并发数量，避免API限制
        semaphore = asyncio.Semaphore(1)  # 头条号API通常有严格的频率限制
        
        async def publish_single(video: GeneratedVideo) -> PublishResult:
            async with semaphore:
                result = await self.publish_video_story(video)
                # 添加延迟，避免频率限制
                await asyncio.sleep(10)
                return result
        
        tasks = [publish_single(video) for video in videos]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"发布视频失败 {videos[i].title}: {result}")
                processed_results.append(PublishResult(
                    success=False,
                    error_message=str(result)
                ))
            else:
                processed_results.append(result)
        
        success_count = sum(1 for r in processed_results if r.success)
        logger.info(f"批量发布完成: 成功{success_count}/{len(videos)}")
        
        return processed_results
    
    def is_configured(self) -> bool:
        """检查是否已配置"""
        return all([
            self.api_client.app_id,
            self.api_client.app_secret
        ])
    
    async def test_connection(self) -> bool:
        """测试API连接"""
        try:
            # 这里可以调用一个简单的API来测试连接
            # 比如获取账号信息
            logger.info("测试头条号API连接...")
            # 实际实现中应该调用真实的测试API
            return True
            
        except Exception as e:
            logger.error(f"头条号API连接测试失败: {e}")
            return False
