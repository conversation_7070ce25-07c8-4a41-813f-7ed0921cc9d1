"""
日志工具模块
"""
import os
import sys
from pathlib import Path
from loguru import logger
from src.config.settings import get_settings

settings = get_settings()


def setup_logger():
    """配置日志系统"""
    
    # 移除默认的控制台处理器
    logger.remove()
    
    # 确保日志目录存在
    log_dir = Path(settings.logs_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 控制台输出配置
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        level=settings.log_level,
        colorize=True
    )
    
    # 通用日志文件
    logger.add(
        log_dir / "app.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        level="INFO",
        rotation="1 day",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 错误日志文件
    logger.add(
        log_dir / "error.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        level="ERROR",
        rotation="1 day",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 任务执行日志
    logger.add(
        log_dir / "tasks.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {extra[task_type]} | {message}",
        level="INFO",
        rotation="1 day",
        retention="30 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: "task_type" in record["extra"]
    )
    
    return logger


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger


def log_task_start(task_name: str, task_id: str = None):
    """记录任务开始"""
    logger.bind(task_type=task_name).info(f"任务开始执行 - ID: {task_id or 'N/A'}")


def log_task_end(task_name: str, task_id: str = None, success: bool = True, error: str = None):
    """记录任务结束"""
    status = "成功" if success else "失败"
    message = f"任务执行{status} - ID: {task_id or 'N/A'}"
    if error:
        message += f" - 错误: {error}"
    
    if success:
        logger.bind(task_type=task_name).info(message)
    else:
        logger.bind(task_type=task_name).error(message)


def log_api_call(api_name: str, endpoint: str, status_code: int = None, response_time: float = None):
    """记录API调用"""
    message = f"API调用 - {api_name} - {endpoint}"
    if status_code:
        message += f" - 状态码: {status_code}"
    if response_time:
        message += f" - 响应时间: {response_time:.2f}s"
    
    logger.info(message)


# 初始化日志系统
setup_logger()
