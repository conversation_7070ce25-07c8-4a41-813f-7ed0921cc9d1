"""
数据库操作模块
"""
import os
import json
from datetime import datetime
from typing import Optional, List, Dict, Any
from pathlib import Path
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Float, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.sqlite import J<PERSON><PERSON>

from src.config.settings import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()

Base = declarative_base()


class NewsArticleRecord(Base):
    """新闻文章记录"""
    __tablename__ = "news_articles"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(500), nullable=False)
    content = Column(Text)
    url = Column(String(1000))
    source = Column(String(100))
    publish_time = Column(DateTime)
    author = Column(String(200))
    tags = Column(JSON)
    hot_score = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.now)
    processed = Column(Boolean, default=False)


class ExtractedStoryRecord(Base):
    """提取的故事记录"""
    __tablename__ = "extracted_stories"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    story_type = Column(String(100))
    main_characters = Column(JSON)
    summary = Column(Text)
    quality_score = Column(Float, default=0.0)
    is_suitable = Column(Boolean, default=False)
    source_article_id = Column(Integer)
    extraction_time = Column(DateTime, default=datetime.now)
    processed = Column(Boolean, default=False)


class RewrittenStoryRecord(Base):
    """改写故事记录"""
    __tablename__ = "rewritten_stories"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    theme = Column(String(200))
    animal_characters = Column(JSON)
    video_titles = Column(JSON)
    video_tags = Column(JSON)
    original_story_id = Column(Integer)
    rewrite_time = Column(DateTime, default=datetime.now)
    processed = Column(Boolean, default=False)


class GeneratedVideoRecord(Base):
    """生成视频记录"""
    __tablename__ = "generated_videos"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    video_file = Column(String(1000), nullable=False)
    title = Column(String(500), nullable=False)
    description = Column(Text)
    tags = Column(JSON)
    duration = Column(Float)
    story_id = Column(Integer)
    generation_time = Column(DateTime, default=datetime.now)
    published = Column(Boolean, default=False)


class PublishResultRecord(Base):
    """发布结果记录"""
    __tablename__ = "publish_results"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    video_id = Column(Integer)
    success = Column(Boolean, nullable=False)
    article_id = Column(String(100))
    platform_video_id = Column(String(100))
    error_message = Column(Text)
    publish_time = Column(DateTime, default=datetime.now)


class TaskExecutionRecord(Base):
    """任务执行记录"""
    __tablename__ = "task_executions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_name = Column(String(100), nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime)
    success = Column(Boolean)
    error_message = Column(Text)
    articles_processed = Column(Integer, default=0)
    stories_extracted = Column(Integer, default=0)
    videos_generated = Column(Integer, default=0)
    videos_published = Column(Integer, default=0)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        try:
            # 确保数据目录存在
            data_dir = Path(settings.data_dir)
            data_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建数据库引擎
            self.engine = create_engine(
                settings.database_url,
                echo=settings.debug,
                pool_pre_ping=True
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # 创建表
            Base.metadata.create_all(bind=self.engine)
            
            logger.info("数据库初始化成功")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def save_news_article(self, article_data: Dict[str, Any]) -> Optional[int]:
        """保存新闻文章"""
        try:
            with self.get_session() as session:
                record = NewsArticleRecord(
                    title=article_data.get("title", ""),
                    content=article_data.get("content", ""),
                    url=article_data.get("url", ""),
                    source=article_data.get("source", ""),
                    publish_time=article_data.get("publish_time"),
                    author=article_data.get("author"),
                    tags=article_data.get("tags", []),
                    hot_score=article_data.get("hot_score", 0)
                )
                
                session.add(record)
                session.commit()
                session.refresh(record)
                
                logger.debug(f"保存新闻文章: {record.id}")
                return record.id
                
        except Exception as e:
            logger.error(f"保存新闻文章失败: {e}")
            return None
    
    def save_extracted_story(self, story_data: Dict[str, Any]) -> Optional[int]:
        """保存提取的故事"""
        try:
            with self.get_session() as session:
                record = ExtractedStoryRecord(
                    title=story_data.get("title", ""),
                    content=story_data.get("content", ""),
                    story_type=story_data.get("story_type", ""),
                    main_characters=story_data.get("main_characters", []),
                    summary=story_data.get("summary", ""),
                    quality_score=story_data.get("quality_score", 0.0),
                    is_suitable=story_data.get("is_suitable", False),
                    source_article_id=story_data.get("source_article_id")
                )
                
                session.add(record)
                session.commit()
                session.refresh(record)
                
                logger.debug(f"保存提取故事: {record.id}")
                return record.id
                
        except Exception as e:
            logger.error(f"保存提取故事失败: {e}")
            return None
    
    def save_rewritten_story(self, story_data: Dict[str, Any]) -> Optional[int]:
        """保存改写故事"""
        try:
            with self.get_session() as session:
                record = RewrittenStoryRecord(
                    title=story_data.get("title", ""),
                    content=story_data.get("content", ""),
                    theme=story_data.get("theme", ""),
                    animal_characters=story_data.get("animal_characters", []),
                    video_titles=story_data.get("video_titles", []),
                    video_tags=story_data.get("video_tags", []),
                    original_story_id=story_data.get("original_story_id")
                )
                
                session.add(record)
                session.commit()
                session.refresh(record)
                
                logger.debug(f"保存改写故事: {record.id}")
                return record.id
                
        except Exception as e:
            logger.error(f"保存改写故事失败: {e}")
            return None
    
    def save_generated_video(self, video_data: Dict[str, Any]) -> Optional[int]:
        """保存生成视频"""
        try:
            with self.get_session() as session:
                record = GeneratedVideoRecord(
                    video_file=video_data.get("video_file", ""),
                    title=video_data.get("title", ""),
                    description=video_data.get("description", ""),
                    tags=video_data.get("tags", []),
                    duration=video_data.get("duration", 0.0),
                    story_id=video_data.get("story_id")
                )
                
                session.add(record)
                session.commit()
                session.refresh(record)
                
                logger.debug(f"保存生成视频: {record.id}")
                return record.id
                
        except Exception as e:
            logger.error(f"保存生成视频失败: {e}")
            return None
    
    def save_publish_result(self, result_data: Dict[str, Any]) -> Optional[int]:
        """保存发布结果"""
        try:
            with self.get_session() as session:
                record = PublishResultRecord(
                    video_id=result_data.get("video_id"),
                    success=result_data.get("success", False),
                    article_id=result_data.get("article_id"),
                    platform_video_id=result_data.get("platform_video_id"),
                    error_message=result_data.get("error_message")
                )
                
                session.add(record)
                session.commit()
                session.refresh(record)
                
                logger.debug(f"保存发布结果: {record.id}")
                return record.id
                
        except Exception as e:
            logger.error(f"保存发布结果失败: {e}")
            return None
    
    def save_task_execution(self, task_data: Dict[str, Any]) -> Optional[int]:
        """保存任务执行记录"""
        try:
            with self.get_session() as session:
                record = TaskExecutionRecord(
                    task_name=task_data.get("task_name", ""),
                    start_time=task_data.get("start_time", datetime.now()),
                    end_time=task_data.get("end_time"),
                    success=task_data.get("success"),
                    error_message=task_data.get("error_message"),
                    articles_processed=task_data.get("articles_processed", 0),
                    stories_extracted=task_data.get("stories_extracted", 0),
                    videos_generated=task_data.get("videos_generated", 0),
                    videos_published=task_data.get("videos_published", 0)
                )
                
                session.add(record)
                session.commit()
                session.refresh(record)
                
                logger.debug(f"保存任务执行记录: {record.id}")
                return record.id
                
        except Exception as e:
            logger.error(f"保存任务执行记录失败: {e}")
            return None
    
    def get_unprocessed_articles(self, limit: int = 10) -> List[NewsArticleRecord]:
        """获取未处理的文章"""
        try:
            with self.get_session() as session:
                return session.query(NewsArticleRecord)\
                    .filter(NewsArticleRecord.processed == False)\
                    .order_by(NewsArticleRecord.hot_score.desc())\
                    .limit(limit)\
                    .all()
        except Exception as e:
            logger.error(f"获取未处理文章失败: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            with self.get_session() as session:
                stats = {
                    "total_articles": session.query(NewsArticleRecord).count(),
                    "processed_articles": session.query(NewsArticleRecord)\
                        .filter(NewsArticleRecord.processed == True).count(),
                    "total_stories": session.query(ExtractedStoryRecord).count(),
                    "suitable_stories": session.query(ExtractedStoryRecord)\
                        .filter(ExtractedStoryRecord.is_suitable == True).count(),
                    "total_videos": session.query(GeneratedVideoRecord).count(),
                    "published_videos": session.query(GeneratedVideoRecord)\
                        .filter(GeneratedVideoRecord.published == True).count(),
                    "successful_publishes": session.query(PublishResultRecord)\
                        .filter(PublishResultRecord.success == True).count()
                }
                return stats
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}


# 全局数据库管理器实例
db_manager = DatabaseManager()


def get_db_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    return db_manager
