"""
AI服务客户端
支持OpenAI和Anthropic API
"""
import asyncio
import openai
import anthropic
from typing import Optional, Dict, Any, List
from enum import Enum
import time

from src.utils.logger import get_logger, log_api_call
from src.config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


class AIProvider(Enum):
    """AI服务提供商"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    DEEPSEEK = "deepseek"


class AIClient:
    """AI客户端统一接口"""
    
    def __init__(self):
        self.openai_client = None
        self.anthropic_client = None
        self.deepseek_client = None
        self._init_clients()
        
    def _init_clients(self):
        """初始化AI客户端"""
        if settings.openai_api_key:
            self.openai_client = openai.AsyncOpenAI(
                api_key=settings.openai_api_key
            )
            logger.info("OpenAI客户端初始化成功")
            
        if settings.anthropic_api_key:
            self.anthropic_client = anthropic.AsyncAnthropic(
                api_key=settings.anthropic_api_key
            )
            logger.info("Anthropic客户端初始化成功")

        # DeepSeek使用OpenAI兼容的API
        deepseek_key = getattr(settings, 'deepseek_api_key', None) or settings.openai_api_key
        if deepseek_key and ("deepseek" in deepseek_key.lower() or "sk-" in deepseek_key):
            self.deepseek_client = openai.AsyncOpenAI(
                api_key=deepseek_key,
                base_url="https://api.deepseek.com"
            )
            logger.info("DeepSeek客户端初始化成功")

        if not any([self.openai_client, self.anthropic_client, self.deepseek_client]):
            logger.warning("未配置任何AI服务API密钥")
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        provider: AIProvider = AIProvider.OPENAI,
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Optional[str]:
        """
        统一的聊天完成接口
        
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "..."}]
            provider: AI服务提供商
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
            
        Returns:
            AI回复内容
        """
        start_time = time.time()
        
        try:
            if provider == AIProvider.OPENAI and self.openai_client:
                return await self._openai_chat_completion(
                    messages, model, temperature, max_tokens, **kwargs
                )
            elif provider == AIProvider.ANTHROPIC and self.anthropic_client:
                return await self._anthropic_chat_completion(
                    messages, model, temperature, max_tokens, **kwargs
                )
            elif provider == AIProvider.DEEPSEEK and self.deepseek_client:
                return await self._deepseek_chat_completion(
                    messages, model, temperature, max_tokens, **kwargs
                )
            else:
                logger.error(f"不支持的AI提供商或未配置: {provider}")
                return None
                
        except Exception as e:
            logger.error(f"AI聊天完成失败: {e}")
            return None
        finally:
            response_time = time.time() - start_time
            log_api_call(provider.value, "chat_completion", response_time=response_time)
    
    async def _openai_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Optional[str]:
        """OpenAI聊天完成"""
        if not model:
            model = "gpt-3.5-turbo"
            
        try:
            response = await self.openai_client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            return None
    
    async def _anthropic_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Optional[str]:
        """Anthropic聊天完成"""
        if not model:
            model = "claude-3-sonnet-20240229"
            
        try:
            # 转换消息格式
            if messages and messages[0]["role"] == "system":
                system_message = messages[0]["content"]
                user_messages = messages[1:]
            else:
                system_message = ""
                user_messages = messages
            
            response = await self.anthropic_client.messages.create(
                model=model,
                max_tokens=max_tokens or 1000,
                temperature=temperature,
                system=system_message,
                messages=user_messages,
                **kwargs
            )
            
            return response.content[0].text
            
        except Exception as e:
            logger.error(f"Anthropic API调用失败: {e}")
            return None

    async def _deepseek_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Optional[str]:
        """DeepSeek聊天完成"""
        if not model:
            model = "deepseek-chat"

        try:
            response = await self.deepseek_client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {e}")
            return None
    
    async def simple_completion(
        self,
        prompt: str,
        provider: AIProvider = AIProvider.OPENAI,
        **kwargs
    ) -> Optional[str]:
        """简单的文本完成接口"""
        messages = [{"role": "user", "content": prompt}]
        return await self.chat_completion(messages, provider, **kwargs)
    
    async def batch_completion(
        self,
        prompts: List[str],
        provider: AIProvider = AIProvider.OPENAI,
        max_concurrent: int = 3,
        **kwargs
    ) -> List[Optional[str]]:
        """批量文本完成"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_prompt(prompt: str) -> Optional[str]:
            async with semaphore:
                return await self.simple_completion(prompt, provider, **kwargs)
        
        tasks = [process_prompt(prompt) for prompt in prompts]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"批量处理出错: {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)
        
        return processed_results
    
    def is_available(self, provider: AIProvider) -> bool:
        """检查AI服务是否可用"""
        if provider == AIProvider.OPENAI:
            return self.openai_client is not None
        elif provider == AIProvider.ANTHROPIC:
            return self.anthropic_client is not None
        elif provider == AIProvider.DEEPSEEK:
            return self.deepseek_client is not None
        return False
    
    def get_available_providers(self) -> List[AIProvider]:
        """获取可用的AI服务提供商"""
        providers = []
        if self.openai_client:
            providers.append(AIProvider.OPENAI)
        if self.anthropic_client:
            providers.append(AIProvider.ANTHROPIC)
        if self.deepseek_client:
            providers.append(AIProvider.DEEPSEEK)
        return providers


# 全局AI客户端实例
ai_client = AIClient()


def get_ai_client() -> AIClient:
    """获取AI客户端实例"""
    return ai_client
