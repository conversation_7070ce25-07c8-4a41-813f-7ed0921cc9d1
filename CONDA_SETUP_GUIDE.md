# Anaconda环境设置指南

## 🎉 恭喜！系统已成功设置

你的自动化内容创作和发布系统已经在Anaconda环境中成功设置并通过了所有基础测试。

## 📁 项目结构

```
makeMoneyPlan/
├── src/                    # 源代码
│   ├── core/              # 核心业务逻辑
│   ├── utils/             # 工具函数
│   ├── config/            # 配置管理
│   └── scheduler/         # 定时任务
├── data/                  # 数据存储
├── logs/                  # 日志文件
├── start_conda.sh         # Conda环境启动脚本
├── test_conda_env.sh      # 环境测试脚本
├── quick_start.py         # 快速测试脚本
└── .env                   # 环境配置文件
```

## 🚀 快速开始

### 1. 激活Conda环境
```bash
eval "$(conda shell.bash hook)"
conda activate money-plan
```

### 2. 配置API密钥
编辑 `.env` 文件，添加你的API密钥：

```env
# AI服务API密钥
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 头条号API配置
TOUTIAO_APP_ID=your_toutiao_app_id
TOUTIAO_APP_SECRET=your_toutiao_app_secret
TOUTIAO_ACCESS_TOKEN=your_toutiao_access_token
```

### 3. 启动系统
```bash
./start_conda.sh
```

### 4. 监控运行状态
```bash
tail -f logs/app.log
```

## 🔧 可用脚本

- `./start_conda.sh` - 启动完整系统
- `./test_conda_env.sh` - 测试环境依赖
- `python quick_start.py` - 快速系统测试

## 📊 系统功能

### ✅ 已实现功能
- **热点文章搜索** - 从百度热搜、微博热搜、知乎热榜获取内容
- **故事内容提取** - AI识别和提取故事性内容
- **动物角色转换** - 将人物故事改写为动物角色版本
- **视频生成** - 自动生成配音和字幕视频（需要额外配置）
- **头条号发布** - 自动发布到头条号平台
- **定时任务** - 每3小时自动执行一次
- **数据库存储** - 完整的数据记录和统计
- **日志监控** - 详细的运行日志

### ⚠️ 注意事项
- **视频生成功能**：由于MoviePy依赖问题，当前使用模拟模式。如需完整视频功能，请手动安装：
  ```bash
  conda activate money-plan
  pip install moviepy==1.0.3
  ```

## 🔑 API密钥获取

### OpenAI API
1. 访问 [OpenAI Platform](https://platform.openai.com/)
2. 注册账号并创建API密钥
3. 将密钥填入 `OPENAI_API_KEY`

### Anthropic API
1. 访问 [Anthropic Console](https://console.anthropic.com/)
2. 注册账号并创建API密钥
3. 将密钥填入 `ANTHROPIC_API_KEY`

### 头条号API
1. 访问 [头条号开放平台](https://open.toutiao.com/)
2. 注册开发者账号
3. 创建应用获取相关信息

## 💰 收益模式

- **广告分成**：视频播放量带来的广告收益
- **创作激励**：平台的创作者激励计划
- **品牌合作**：优质内容吸引品牌合作
- **知识付费**：后期可扩展知识付费内容

## 🛠️ 故障排除

### 环境问题
```bash
# 重新测试环境
./test_conda_env.sh

# 重新激活环境
conda deactivate
conda activate money-plan
```

### 依赖问题
```bash
# 重新安装依赖
pip install -r requirements_core.txt
```

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看任务日志
tail -f logs/tasks.log
```

## 📈 系统监控

### 查看统计信息
```bash
python -c "from src.utils.database import get_db_manager; print(get_db_manager().get_statistics())"
```

### 手动执行任务
```bash
python -c "
import asyncio
from src.scheduler.tasks import TaskScheduler
scheduler = TaskScheduler()
asyncio.run(scheduler.run_pipeline_now())
"
```

## 🎯 下一步计划

1. **配置API密钥** - 获取并配置所需的API服务
2. **测试运行** - 启动系统并观察运行状态
3. **内容优化** - 根据实际效果调整内容策略
4. **扩展功能** - 添加更多新闻源和发布平台

## 📞 技术支持

如果遇到问题，请检查：
1. Conda环境是否正确激活
2. 所有依赖是否正确安装
3. API密钥是否正确配置
4. 网络连接是否正常

---

🎉 **恭喜你！自动化内容创作系统已经准备就绪，开始你的赚钱之旅吧！** 💰
