#!/bin/bash
# 测试conda环境

eval "$(conda shell.bash hook)"
conda activate money-plan

echo "测试conda环境..."
echo "Python版本: $(python --version)"
echo "Conda环境: $CONDA_DEFAULT_ENV"

# 测试关键依赖
python -c "
import sys
print(f'Python路径: {sys.executable}')

# 测试关键模块
modules = [
    'fastapi', 'uvicorn', 'pydantic', 'dotenv',
    'openai', 'anthropic', 'pandas', 'numpy', 'requests',
    'bs4', 'sqlalchemy', 'apscheduler',
    'pydub', 'aiohttp', 'loguru'
]

failed = []
for module in modules:
    try:
        __import__(module)
        print(f'✓ {module}')
    except ImportError as e:
        print(f'✗ {module}: {e}')
        failed.append(module)

if failed:
    print(f'\\n失败的模块: {failed}')
    sys.exit(1)
else:
    print('\\n所有模块测试通过!')
"

echo "环境测试完成"
