#!/usr/bin/env python3
"""
自动化内容创作和发布系统 - 主程序入口
"""
import asyncio
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.utils.logger import get_logger, log_task_start, log_task_end
from src.config.settings import get_settings
from src.scheduler.tasks import TaskScheduler

logger = get_logger(__name__)
settings = get_settings()


class Application:
    """主应用程序类"""
    
    def __init__(self):
        self.scheduler = None
        self.running = False
        
    async def startup(self):
        """应用启动"""
        logger.info("=" * 50)
        logger.info(f"启动 {settings.app_name} v{settings.version}")
        logger.info(f"环境: {settings.environment}")
        logger.info(f"调试模式: {settings.debug}")
        logger.info("=" * 50)
        
        # 创建必要的目录
        self._create_directories()
        
        # 初始化调度器
        self.scheduler = TaskScheduler()
        
        # 启动定时任务
        await self.scheduler.start()
        
        self.running = True
        logger.info("应用启动完成")
        
    async def shutdown(self):
        """应用关闭"""
        logger.info("正在关闭应用...")
        self.running = False
        
        if self.scheduler:
            await self.scheduler.stop()
            
        logger.info("应用已关闭")
        
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            settings.data_dir,
            settings.logs_dir,
            settings.temp_dir,
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.debug(f"确保目录存在: {directory}")
    
    async def run(self):
        """运行应用"""
        try:
            await self.startup()
            
            # 保持应用运行
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭...")
        except Exception as e:
            logger.error(f"应用运行出错: {e}")
            raise
        finally:
            await self.shutdown()


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，准备关闭应用")
    sys.exit(0)


async def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建并运行应用
    app = Application()
    await app.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)
